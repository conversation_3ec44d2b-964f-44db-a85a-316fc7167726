import React from 'react'
import <PERSON><PERSON>D<PERSON> from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import { AuthInitializer } from '@/components/auth/AuthInitializer'
import App from './App.tsx'
import './index.css'

// Global error handler for unhandled errors
window.addEventListener('error', (event) => {
  console.error('🚨 [GLOBAL ERROR]:', event.error)
  if (event.error?.message?.includes('Cannot read properties of undefined')) {
    console.error('🔍 [UNDEFINED PROPERTY ERROR]:', {
      message: event.error.message,
      stack: event.error.stack,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  }
})

// Global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 [UNHANDLED PROMISE REJECTION]:', event.reason)
  if (event.reason?.message?.includes('Cannot read properties of undefined')) {
    console.error('🔍 [UNDEFINED PROPERTY PROMISE ERROR]:', event.reason)
  }
})

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 401, 403, 404, or 429 errors
        if (error?.response?.status === 401 ||
            error?.response?.status === 403 ||
            error?.response?.status === 404 ||
            error?.response?.status === 429) {
          return false
        }
        return failureCount < 2
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <AuthInitializer>
          <App />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1e293b',
                color: '#f1f5f9',
                border: '1px solid #334155',
              },
              success: {
                iconTheme: {
                  primary: '#22c55e',
                  secondary: '#f1f5f9',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#f1f5f9',
                },
              },
            }}
          />
        </AuthInitializer>
      </BrowserRouter>
    </QueryClientProvider>
  </React.StrictMode>
)
